<?php

namespace App\Console\Commands;

use App\Models\Blog;
use Illuminate\Console\Command;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitemap:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate the sitemap and save it to public/sitemap.xml';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating sitemap...');

        $sitemap = Sitemap::create();

        // Add static pages
        $this->addStaticPages($sitemap);

        // Add blog pages
        $this->addBlogPages($sitemap);

        // Write sitemap to public directory
        $sitemap->writeToFile(public_path('sitemap.xml'));

        $this->info('Sitemap generated successfully at public/sitemap.xml');

        return Command::SUCCESS;
    }

    /**
     * Add static pages to the sitemap
     */
    private function addStaticPages(Sitemap $sitemap): void
    {
        $baseUrl = config('app.url');

        // Home page
        $sitemap->add(
            Url::create($baseUrl . '/')
                ->setLastModificationDate(now()->subDays(7))
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                ->setPriority(1.0)
        );

        // Roadmap
        $sitemap->add(
            Url::create($baseUrl . '/roadmap')
                ->setLastModificationDate(now()->subMonth())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.8)
        );

        // Documentation
        $sitemap->add(
            Url::create($baseUrl . '/documentation')
                ->setLastModificationDate(now()->subMonth())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.8)
        );

        // Terms of Service
        $sitemap->add(
            Url::create($baseUrl . '/terms-of-service')
                ->setLastModificationDate(now()->subYear())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY)
                ->setPriority(0.5)
        );

        // Privacy Policy
        $sitemap->add(
            Url::create($baseUrl . '/privacy-policy')
                ->setLastModificationDate(now()->subYear())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY)
                ->setPriority(0.5)
        );

        // Refund Policy
        $sitemap->add(
            Url::create($baseUrl . '/refund-policy')
                ->setLastModificationDate(now()->subYear())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY)
                ->setPriority(0.5)
        );

        // Tools - Pricing Calculator
        $sitemap->add(
            Url::create($baseUrl . '/tools/pricing-calculator')
                ->setLastModificationDate(now()->subMonth())
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.9)
        );
    }

    /**
     * Add blog pages to the sitemap
     */
    private function addBlogPages(Sitemap $sitemap): void
    {
        $baseUrl = config('app.url');

        // Blog index page
        $latestBlogUpdate = Blog::published()->latest()->first()?->updated_at ?? now()->subWeek();
        $sitemap->add(
            Url::create($baseUrl . '/blog')
                ->setLastModificationDate($latestBlogUpdate)
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                ->setPriority(0.8)
        );

        // Individual blog posts
        Blog::published()
            ->select(['slug', 'updated_at', 'post_date'])
            ->chunk(100, function ($blogs) use ($sitemap, $baseUrl) {
                foreach ($blogs as $blog) {
                    $sitemap->add(
                        Url::create($baseUrl . '/blog/' . $blog->slug)
                            ->setLastModificationDate($blog->updated_at)
                            ->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY)
                            ->setPriority(0.7)
                    );
                }
            });
    }
}
