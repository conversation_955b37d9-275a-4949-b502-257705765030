<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use Illuminate\Http\Request;
use Spatie\Sitemap\Sitemap;
use Spa<PERSON>\Sitemap\Tags\Url;

class SitemapController extends Controller
{
    /**
     * Generate and return the sitemap
     */
    public function index()
    {
        $sitemap = Sitemap::create();

        // Add static pages
        $this->addStaticPages($sitemap);
        
        // Add blog pages
        $this->addBlogPages($sitemap);

        return $sitemap->toResponse(request());
    }

    /**
     * Add static pages to the sitemap
     */
    private function addStaticPages(Sitemap $sitemap): void
    {
        $baseUrl = config('app.url');
        
        // Home page
        $sitemap->add(
            Url::create($baseUrl . '/')
                ->setLastModificationDate(now()->subDays(7)) // Updated weekly
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                ->setPriority(1.0)
        );

        // Roadmap
        $sitemap->add(
            Url::create($baseUrl . '/roadmap')
                ->setLastModificationDate(now()->subMonth()) // Updated monthly
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.8)
        );

        // Documentation
        $sitemap->add(
            Url::create($baseUrl . '/documentation')
                ->setLastModificationDate(now()->subMonth()) // Updated monthly
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.8)
        );

        // Terms of Service
        $sitemap->add(
            Url::create($baseUrl . '/terms-of-service')
                ->setLastModificationDate(now()->subYear()) // Updated yearly
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY)
                ->setPriority(0.5)
        );

        // Privacy Policy
        $sitemap->add(
            Url::create($baseUrl . '/privacy-policy')
                ->setLastModificationDate(now()->subYear()) // Updated yearly
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY)
                ->setPriority(0.5)
        );

        // Refund Policy
        $sitemap->add(
            Url::create($baseUrl . '/refund-policy')
                ->setLastModificationDate(now()->subYear()) // Updated yearly
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY)
                ->setPriority(0.5)
        );

        // Tools - Pricing Calculator
        $sitemap->add(
            Url::create($baseUrl . '/tools/pricing-calculator')
                ->setLastModificationDate(now()->subMonth()) // Updated monthly
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                ->setPriority(0.9)
        );
    }

    /**
     * Add blog pages to the sitemap
     */
    private function addBlogPages(Sitemap $sitemap): void
    {
        $baseUrl = config('app.url');
        
        // Blog index page
        $latestBlogUpdate = Blog::published()->latest()->first()?->updated_at ?? now()->subWeek();
        $sitemap->add(
            Url::create($baseUrl . '/blog')
                ->setLastModificationDate($latestBlogUpdate)
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
                ->setPriority(0.8)
        );

        // Individual blog posts
        Blog::published()
            ->select(['slug', 'updated_at', 'post_date'])
            ->chunk(100, function ($blogs) use ($sitemap, $baseUrl) {
                foreach ($blogs as $blog) {
                    $sitemap->add(
                        Url::create($baseUrl . '/blog/' . $blog->slug)
                            ->setLastModificationDate($blog->updated_at)
                            ->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY)
                            ->setPriority(0.7)
                    );
                }
            });
    }
}
